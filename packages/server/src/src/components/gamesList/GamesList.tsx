import { Box, Pagination } from "@mui/material"
import { useCallback } from "react"

import { GAMES_PER_PAGE } from "../../config/game.conf"
import { ICNavigationProps } from "../GameThumbnail/GameThumbnail"

import {
  GameThumbnailWrapper,
  ICUserThumbnailWrapperGame,
} from "./GameThumbnailWrapper"

interface GamesListProps {
  games: ICUserThumbnailWrapperGame[]
  onUser?: (gameId: number) => void
  onPageChange: (page: number) => void
  page?: number
  communityId?: number
  navigation?: ICNavigationProps
}
export const GamesList = ({
  games,
  page,
  onUser,
  communityId,
  onPageChange,
  navigation = {},
}: GamesListProps) => {
  const onChange = useCallback(
    (_: React.ChangeEvent<unknown>, page: number) => {
      onPageChange(page)
    },
    [onPageChange],
  )

  const pages = Math.ceil(games.length / GAMES_PER_PAGE)
  const startItem = GAMES_PER_PAGE * ((page ?? 1) - 1)
  const endItem = startItem + GAMES_PER_PAGE

  const navParams = navigation?.params !== true ? navigation?.params : {}

  return (
    <Box
      display="flex"
      justifyContent="center"
      flexDirection="column"
      gap={4}
      pt={2}
    >
      <Box
        display="flex"
        justifyContent="center"
        flexDirection="row"
        alignItems="center"
      >
        <Pagination
          shape="rounded"
          count={pages}
          page={page}
          variant="outlined"
          onChange={onChange}
        />
      </Box>
      <Box display="flex" gap={2} flexWrap="wrap" justifyContent="center">
        {games.slice(startItem, endItem).map((game) => (
          <GameThumbnailWrapper
            navigation={{
              ...navigation,
              params: { ...navParams, gameId: String(game.id) },
            }}
            onUser={onUser}
            game={game}
            communityId={communityId}
            key={game.id}
          />
        ))}
      </Box>
      <Box
        display="flex"
        justifyContent="center"
        flexDirection="row"
        alignItems="center"
        pb={4}
      >
        <Pagination
          shape="rounded"
          count={pages}
          page={page}
          variant="outlined"
          onChange={onChange}
        />
      </Box>
    </Box>
  )
}
