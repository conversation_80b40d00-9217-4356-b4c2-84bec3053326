import dayjs from "dayjs"
import { memo, useMemo } from "react"

import { LAST_UPDATE_PERIOD } from "../../config/game.conf"
import { useGameStore } from "../../store/useGamesStore"
import {
  GameThumbnail,
  ICNavigationProps,
  ICThumbnailGame,
} from "../GameThumbnail/GameThumbnail"
import { ICFAvatarOnClick } from "../UserAvatar/UserAvatar"

export type ICUserThumbnailWrapperGame = ICThumbnailGame & {
  news?: string | null
  users?: [number, number][]
}

interface GameThumbnailProps {
  game: ICUserThumbnailWrapperGame
  communityId?: number
  onUser?: ICFAvatarOnClick
  navigation?: ICNavigationProps
}
export const GameThumbnailWrapper = memo(({
  game,
  communityId,
  onUser,
  navigation,
}: GameThumbnailProps) => {
  const { getPopulatedUser } = useGameStore()
  const isNew = game.news
    ? !dayjs(game.news).isBefore(dayjs(), LAST_UPDATE_PERIOD)
    : false

  const userList = useMemo(() => {
    return game.users
      ? game.users
          .sort((a, b) => (a[1] > b[1] ? -1 : 1))
          .map(
            (user: [number, number]) => getPopulatedUser(user[0]) ?? undefined,
          )
          .filter((user) => user !== undefined)
      : undefined
  }, [game.users, getPopulatedUser])

  return (
    <GameThumbnail
      navigation={navigation}
      game={game}
      communityId={communityId}
      onUser={onUser}
      userList={userList}
      isNew={isNew}
    />
  )
})
